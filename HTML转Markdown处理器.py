#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML教程转Markdown处理器
用于将HTML格式的教程文件转换为结构化的Markdown文档

作者：AI助手
创建时间：2025年1月
"""

import os
import re
import json
from pathlib import Path
from bs4 import BeautifulSoup, NavigableString
from urllib.parse import urljoin, urlparse
import html2text
from typing import Dict, List, Tuple, Optional


class HTMLToMarkdownConverter:
    """HTML转Markdown转换器"""
    
    def __init__(self, input_dir: str, output_dir: str = "输出文档"):
        """
        初始化转换器
        
        Args:
            input_dir: 输入HTML文件目录
            output_dir: 输出Markdown文件目录
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 配置html2text转换器
        self.h2t = html2text.HTML2Text()
        self.h2t.ignore_links = False
        self.h2t.ignore_images = False
        self.h2t.ignore_emphasis = False
        self.h2t.body_width = 0  # 不限制行宽
        self.h2t.unicode_snob = True
        self.h2t.escape_snob = True
        
        # 存储文档结构信息
        self.doc_structure = {}
        self.processed_files = []
        
    def extract_title_from_html(self, soup: BeautifulSoup) -> str:
        """从HTML中提取标题"""
        # 尝试从多个位置提取标题
        title_selectors = [
            'h1',
            '.theme-doc-markdown h1',
            'header h1',
            'title'
        ]
        
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text().strip()
                # 清理标题中的特殊字符
                title = re.sub(r'[🛰️🔎🔦🚀🛫🧰]+\s*', '', title)
                if title:
                    return title
        
        return "未知标题"
    
    def extract_main_content(self, soup: BeautifulSoup) -> BeautifulSoup:
        """提取主要内容区域"""
        # 定义主要内容的选择器（按优先级排序）
        content_selectors = [
            '.theme-doc-markdown.markdown',
            '.markdown',
            'main article',
            'main .container',
            '.docItemContainer_Djhp article',
            'main'
        ]
        
        for selector in content_selectors:
            content = soup.select_one(selector)
            if content:
                return content
        
        # 如果没有找到特定的内容区域，返回body
        return soup.find('body') or soup
    
    def clean_content(self, content: BeautifulSoup) -> BeautifulSoup:
        """清理内容，移除不需要的元素"""
        # 要移除的元素选择器
        remove_selectors = [
            'nav',
            '.navbar',
            '.sidebar',
            '.breadcrumbs',
            '.toc',
            '.theme-doc-toc-mobile',
            'button',
            'script',
            'style',
            '.announcementBar_mb4j',
            '.theme-back-to-top-button',
            '.collapseSidebarButton_PEFL'
        ]
        
        # 移除不需要的元素
        for selector in remove_selectors:
            for elem in content.select(selector):
                elem.decompose()
        
        # 清理空的段落和div
        for tag in content.find_all(['p', 'div']):
            if not tag.get_text().strip() and not tag.find(['img', 'video', 'audio']):
                tag.decompose()
        
        return content
    
    def process_images(self, content: BeautifulSoup, base_url: str = "") -> BeautifulSoup:
        """处理图片链接"""
        for img in content.find_all('img'):
            src = img.get('src', '')
            if src:
                # 处理base64图片
                if src.startswith('data:'):
                    # 为base64图片生成描述性文本
                    alt_text = img.get('alt', '图片')
                    img.replace_with(f"[{alt_text}]")
                else:
                    # 处理相对路径
                    if not src.startswith(('http://', 'https://')):
                        if base_url:
                            src = urljoin(base_url, src)
                    img['src'] = src
        
        return content
    
    def process_links(self, content: BeautifulSoup) -> BeautifulSoup:
        """处理链接"""
        for link in content.find_all('a'):
            href = link.get('href', '')
            if href:
                # 处理相对链接，转换为对应的markdown文件
                if href.endswith('.html'):
                    # 将.html链接转换为.md链接
                    href = href.replace('.html', '.md')
                    link['href'] = href
        
        return content
    
    def generate_filename(self, title: str, original_filename: str) -> str:
        """生成中文文件名"""
        # 清理标题，移除特殊字符
        clean_title = re.sub(r'[<>:"/\\|?*]', '', title)
        clean_title = clean_title.strip()
        
        # 如果标题为空或只有特殊字符，使用原文件名
        if not clean_title or clean_title == "未知标题":
            base_name = Path(original_filename).stem
            return f"{base_name}.md"
        
        return f"{clean_title}.md"
    
    def convert_html_to_markdown(self, html_file: Path) -> Optional[Tuple[str, str]]:
        """
        将单个HTML文件转换为Markdown
        
        Returns:
            Tuple[filename, markdown_content] 或 None
        """
        try:
            print(f"正在处理: {html_file.name}")
            
            # 读取HTML文件
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 提取标题
            title = self.extract_title_from_html(soup)
            
            # 提取主要内容
            main_content = self.extract_main_content(soup)
            
            # 清理内容
            cleaned_content = self.clean_content(main_content)
            
            # 处理图片和链接
            processed_content = self.process_images(cleaned_content)
            processed_content = self.process_links(processed_content)
            
            # 转换为Markdown
            markdown_content = self.h2t.handle(str(processed_content))
            
            # 清理Markdown内容
            markdown_content = self.clean_markdown(markdown_content, title)
            
            # 生成文件名
            filename = self.generate_filename(title, html_file.name)
            
            return filename, markdown_content
            
        except Exception as e:
            print(f"处理文件 {html_file.name} 时出错: {str(e)}")
            return None
    
    def clean_markdown(self, markdown: str, title: str) -> str:
        """清理Markdown内容"""
        # 移除多余的空行
        markdown = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown)

        # 确保标题格式正确
        if not markdown.startswith('#'):
            markdown = f"# {title}\n\n{markdown}"

        # 将4空格缩进的代码块转换为```格式
        lines = markdown.split('\n')
        cleaned_lines = []
        in_code_block = False
        code_lines = []

        for line in lines:
            # 检测4空格缩进的代码行
            if line.startswith('    ') and line.strip():
                if not in_code_block:
                    in_code_block = True
                    cleaned_lines.append('```python')  # 默认使用python语法高亮
                code_lines.append(line[4:])  # 移除4个空格的缩进
            else:
                if in_code_block:
                    # 结束代码块
                    cleaned_lines.extend(code_lines)
                    cleaned_lines.append('```')
                    in_code_block = False
                    code_lines = []

                # 保留其他格式
                cleaned_lines.append(line.rstrip())

        # 处理文件末尾的代码块
        if in_code_block:
            cleaned_lines.extend(code_lines)
            cleaned_lines.append('```')

        return '\n'.join(cleaned_lines).strip()
    
    def process_all_files(self):
        """处理所有HTML文件（包括子目录）"""
        # 递归查找所有HTML文件
        html_files = list(self.input_dir.rglob('*.html'))

        if not html_files:
            print(f"在目录 {self.input_dir} 中未找到HTML文件")
            return

        print(f"找到 {len(html_files)} 个HTML文件")

        success_count = 0
        for html_file in html_files:
            result = self.convert_html_to_markdown(html_file)
            if result:
                filename, markdown_content = result

                # 处理子目录结构
                relative_path = html_file.relative_to(self.input_dir)
                if relative_path.parent != Path('.'):
                    # 创建对应的子目录
                    output_subdir = self.output_dir / relative_path.parent
                    output_subdir.mkdir(parents=True, exist_ok=True)
                    output_file = output_subdir / filename
                    subdir_info = f" (子目录: {relative_path.parent})"
                else:
                    output_file = self.output_dir / filename
                    subdir_info = ""

                try:
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(markdown_content)

                    self.processed_files.append({
                        'original': str(relative_path),
                        'converted': str(output_file.relative_to(self.output_dir)),
                        'title': self.extract_title_from_content(markdown_content)
                    })

                    success_count += 1
                    print(f"✓ 已转换: {filename}{subdir_info}")

                except Exception as e:
                    print(f"✗ 保存文件 {filename} 时出错: {str(e)}")

        print(f"\n转换完成！成功转换 {success_count}/{len(html_files)} 个文件")

        # 生成处理报告
        self.generate_report()
    
    def extract_title_from_content(self, markdown: str) -> str:
        """从Markdown内容中提取标题"""
        lines = markdown.split('\n')
        for line in lines:
            if line.startswith('# '):
                return line[2:].strip()
        return "未知标题"
    
    def generate_report(self):
        """生成处理报告"""
        report_file = self.output_dir / "转换报告.md"
        
        report_content = f"""# HTML转Markdown转换报告

## 转换概况

- 总文件数: {len(self.processed_files)}
- 转换时间: {self.get_current_time()}
- 输出目录: {self.output_dir}

## 转换文件列表

| 原文件名 | 转换后文件名 | 标题 |
|---------|-------------|------|
"""
        
        for file_info in self.processed_files:
            report_content += f"| {file_info['original']} | {file_info['converted']} | {file_info['title']} |\n"
        
        report_content += f"""
## 使用说明

1. 所有转换后的Markdown文件已保存在 `{self.output_dir}` 目录中
2. 文件名使用中文，便于识别章节内容
3. 代码块已保持语法高亮标记
4. 图片链接已进行相应处理

## 注意事项

- 部分复杂的HTML结构可能需要手动调整
- 建议检查转换后的文件格式是否符合预期
- 如有问题，请参考原HTML文件进行对比修正
"""
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"✓ 已生成转换报告: {report_file}")
        except Exception as e:
            print(f"✗ 生成报告时出错: {str(e)}")
    
    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")


def main():
    """主函数"""
    print("=" * 60)
    print("HTML教程转Markdown处理器")
    print("=" * 60)
    
    # 设置输入和输出目录
    input_directory = "drissionpage教程网页"
    output_directory = "DrissionPage教程文档"
    
    # 检查输入目录是否存在
    if not os.path.exists(input_directory):
        print(f"错误：输入目录 '{input_directory}' 不存在！")
        print("请确保HTML文件位于正确的目录中。")
        return
    
    # 创建转换器实例
    converter = HTMLToMarkdownConverter(input_directory, output_directory)
    
    # 开始转换
    converter.process_all_files()
    
    print("\n" + "=" * 60)
    print("转换完成！请查看输出目录中的Markdown文件。")
    print("=" * 60)


if __name__ == "__main__":
    main()
